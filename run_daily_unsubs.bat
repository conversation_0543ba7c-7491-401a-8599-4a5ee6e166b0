@echo off
echo ===============================================
echo    UNSUBSCRIBE DATA PROCESSING
echo ===============================================
echo Started at: %date% %time%
echo.

REM Clean up existing files before starting
echo Deleting all files in H:\Master Bounces and Unsubs\Postpanel Unsubs...
del /s /q "H:\Master Bounces and Unsubs\Postpanel Unsubs\*.*"
echo Cleanup completed.
echo.

REM Set the scripts directory - using %~dp0 to get the directory where this batch file is located
set "SCRIPTS_DIR=%~dp0"

REM Create a log file
set "LOG_FILE=%SCRIPTS_DIR%script_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Script execution started at %date% %time% > "%LOG_FILE%"

REM Count total scripts
set "TOTAL_SCRIPTS=7"
set "CURRENT_SCRIPT=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: cms_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: cms_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%cms_unsubs_csv_download.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: mg.port1.in_pp_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: mg.port1.in_pp_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%mg.port1.in_pp_unsubs_csv_download.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnusgroup.port1.in_pp_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnusgroup.port1.in_pp_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%magnusgroup.port1.in_pp_unsubs_csv_download.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnus.smtps.in_pp_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnus.smtps.in_pp_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%magnus.smtps.in_pp_unsubs_csv_download.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 1unsubs_from_emailinvite.net_2.0.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 1unsubs_from_emailinvite.net_2.0.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%1unsubs_from_emailinvite.net_2.0.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 2unsubs_from_emailinvitez.com.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 2unsubs_from_emailinvitez.com.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%2unsubs_from_emailinvitez.com.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: daily_unsubs_upload2dbms.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: daily_unsubs_upload2dbms.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%daily_unsubs_upload2dbms.py"
if %errorlevel% neq 0 (
    echo ERROR: Script failed with error code %errorlevel%
    echo ERROR: Script failed with error code %errorlevel% >> "%LOG_FILE%"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
)
echo.
echo. >> "%LOG_FILE%"

echo ===============================================
echo All scripts completed at %date% %time%
echo ===============================================
echo All scripts completed at %date% %time% >> "%LOG_FILE%"

echo Log file saved to: %LOG_FILE%
echo.
echo Press any key to exit...
pause > nul
