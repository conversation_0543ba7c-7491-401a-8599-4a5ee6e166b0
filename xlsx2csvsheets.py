import pandas as pd
import os
import glob

def extract_sheets_to_csv(xlsx_filepath, output_dir):
    """
    Extracts all sheets from an XLSX file and saves each sheet as a separate CSV file.

    Args:
        xlsx_filepath: The path to the XLSX file.
        output_dir: The directory where the CSV files will be saved.
    """

    try:
        # Check if the output directory exists, if not create it
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Read the Excel file (reading all sheets at once is generally more efficient)
        xls = pd.ExcelFile(xlsx_filepath)  # More efficient for multiple sheets

        for sheet_name in xls.sheet_names:
            try:
                df = pd.read_excel(xls, sheet_name=sheet_name)

                # Construct the CSV file path
                csv_filepath = os.path.join(output_dir, f"{sheet_name}.csv")  # Include sheet name

                # Save the DataFrame to a CSV file
                df.to_csv(csv_filepath, index=False, encoding='utf-8')  # UTF-8 encoding is generally recommended

                print(f"Sheet '{sheet_name}' saved to '{csv_filepath}'")

            except Exception as e:
                print(f"Error processing sheet '{sheet_name}': {e}")


    except FileNotFoundError:
        print(f"Error: XLSX file not found at '{xlsx_filepath}'")
    except Exception as e:
        print(f"An error occurred: {e}")

# Get the file path from user input
path = input('Enter file path: ')

# Ensure the path ends with a backslash if it doesn't already
if not path.endswith('\\'):
    path += '\\'

# Use glob to find all .xlsx files in the specified path
xlsx_files = glob.glob(path + '*.xlsx')

# Create the output directory if it doesn't exist
os.makedirs("sheets", exist_ok=True)
output_directory = path + "sheets"

# Loop through each .xlsx file found and process it
for xlsx_file in xlsx_files:
    extract_sheets_to_csv(xlsx_file, output_directory)

print("Extraction complete.")
