@echo off
echo ===============================================
echo    COMPLETE UNSUBSCRIBE PROCESSING SEQUENCE
echo ===============================================
echo Started at: %date% %time%
echo.

REM Set the scripts directory - using %~dp0 to get the directory where this batch file is located
set "SCRIPTS_DIR=%~dp0"

REM Create a master log file
set "MASTER_LOG=%SCRIPTS_DIR%master_unsubs_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Master sequence started at %date% %time% > "%MASTER_LOG%"

REM -----------------------------------------------
echo ===============================================
echo STEP 1/3: Running mas.bat
echo ===============================================
echo STEP 1/3: Running mas.bat >> "%MASTER_LOG%"
echo Start time: %time% >> "%MASTER_LOG%"
echo.

call mas.bat
set STEP1_STATUS=%errorlevel%

if %STEP1_STATUS% neq 0 (
    echo ERROR: mas.bat failed with error code %STEP1_STATUS%
    echo ERROR: mas.bat failed with error code %STEP1_STATUS% >> "%MASTER_LOG%"
) else (
    echo SUCCESS: mas.bat completed successfully
    echo SUCCESS: mas.bat completed successfully >> "%MASTER_LOG%"
)

echo End time: %time% >> "%MASTER_LOG%"
echo. >> "%MASTER_LOG%"
echo.

REM -----------------------------------------------
echo ===============================================
echo STEP 2/3: Running run_upl_unsubs_dbms.bat
echo ===============================================
echo STEP 2/3: Running run_upl_unsubs_dbms.bat >> "%MASTER_LOG%"
echo Start time: %time% >> "%MASTER_LOG%"
echo.

call run_upl_unsubs_dbms.bat
set STEP2_STATUS=%errorlevel%

if %STEP2_STATUS% neq 0 (
    echo ERROR: run_upl_unsubs_dbms.bat failed with error code %STEP2_STATUS%
    echo ERROR: run_upl_unsubs_dbms.bat failed with error code %STEP2_STATUS% >> "%MASTER_LOG%"
) else (
    echo SUCCESS: run_upl_unsubs_dbms.bat completed successfully
    echo SUCCESS: run_upl_unsubs_dbms.bat completed successfully >> "%MASTER_LOG%"
)

echo End time: %time% >> "%MASTER_LOG%"
echo. >> "%MASTER_LOG%"
echo.

REM -----------------------------------------------
echo ===============================================
echo STEP 3/3: Running run_upl_unsubs_pp.bat
echo ===============================================
echo STEP 3/3: Running run_upl_unsubs_pp.bat >> "%MASTER_LOG%"
echo Start time: %time% >> "%MASTER_LOG%"
echo.

call run_upl_unsubs_pp.bat
set STEP3_STATUS=%errorlevel%

if %STEP3_STATUS% neq 0 (
    echo ERROR: run_upl_unsubs_pp.bat failed with error code %STEP3_STATUS%
    echo ERROR: run_upl_unsubs_pp.bat failed with error code %STEP3_STATUS% >> "%MASTER_LOG%"
) else (
    echo SUCCESS: run_upl_unsubs_pp.bat completed successfully
    echo SUCCESS: run_upl_unsubs_pp.bat completed successfully >> "%MASTER_LOG%"
)

echo End time: %time% >> "%MASTER_LOG%"
echo. >> "%MASTER_LOG%"
echo.

REM -----------------------------------------------
echo ===============================================
echo SEQUENCE SUMMARY
echo ===============================================
echo SEQUENCE SUMMARY >> "%MASTER_LOG%"

echo Step 1 (mas.bat): %STEP1_STATUS% - %if %STEP1_STATUS%==0 (echo SUCCESS) else (echo FAILED)%
echo Step 2 (run_upl_unsubs_dbms.bat): %STEP2_STATUS% - %if %STEP2_STATUS%==0 (echo SUCCESS) else (echo FAILED)%
echo Step 3 (run_upl_unsubs_pp.bat): %STEP3_STATUS% - %if %STEP3_STATUS%==0 (echo SUCCESS) else (echo FAILED)%

echo Step 1 (mas.bat): %STEP1_STATUS% - %if %STEP1_STATUS%==0 (echo SUCCESS) else (echo FAILED)% >> "%MASTER_LOG%"
echo Step 2 (run_upl_unsubs_dbms.bat): %STEP2_STATUS% - %if %STEP2_STATUS%==0 (echo SUCCESS) else (echo FAILED)% >> "%MASTER_LOG%"
echo Step 3 (run_upl_unsubs_pp.bat): %STEP3_STATUS% - %if %STEP3_STATUS%==0 (echo SUCCESS) else (echo FAILED)% >> "%MASTER_LOG%"

echo.
echo ===============================================
echo All processes completed at %date% %time%
echo ===============================================
echo All processes completed at %date% %time% >> "%MASTER_LOG%"

echo Master log file saved to: %MASTER_LOG%
echo.
echo Press any key to exit...
pause > nul
