import os
import time
import pandas as pd
import pyautogui  # Import PyAutoGUI at the top level
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException, StaleElementReferenceException
import traceback # For detailed error printing
import random
from dataclasses import dataclass
from typing import Dict, List
from enum import Enum
from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn, TimeElapsedColumn
class UploadStatus(Enum):
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    FAILED_LOGIN = "FAILED (Login Error)"
    SKIPPED_MISSING = "SKIPPED (Missing Data)"
    SKIPPED_NOT_FOUND = "SKIPPED (File Not Found)"

@dataclass
class UploadResult:
    entry_key: str
    status: UploadStatus
    message: str = ""

class ResultTracker:
    def __init__(self):
        self.results: Dict[str, List[UploadResult]] = {}

    def add_result(self, site_name: str, result: UploadResult):
        if site_name not in self.results:
            self.results[site_name] = []
        self.results[site_name].append(result)

    def get_site_summary(self, site_name: str) -> Dict[str, int]:
        site_results = self.results.get(site_name, [])
        return {
            "success": sum(1 for r in site_results if r.status == UploadStatus.SUCCESS),
            "failed": sum(1 for r in site_results if r.status.name.startswith("FAILED")),
            "skipped": sum(1 for r in site_results if r.status.name.startswith("SKIPPED"))
        }

def create_sample_file(file_path):
    """Create a sample Excel file for upload if it doesn't exist."""
    if not os.path.exists(file_path):
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        df = pd.DataFrame({
            'Email': ['<EMAIL>', '<EMAIL>'],
            'Name': ['Sample User', 'Test User']
        })
        df.to_excel(file_path, index=False)
        print(f"Created sample file at: {file_path}")
    else:
        print(f"File already exists at: {file_path}")
    return file_path

# New function: Performs the upload steps within an existing browser session
def perform_single_upload_task(driver, file_path, conference_name, website_url):
    """Performs the steps to upload one file within an active browser session.

    Args:
        driver: The active Selenium WebDriver instance (already logged in).
        file_path (str): Path to the file to upload.
        conference_name (str): Name of the conference to select.
        website_url (str): URL of the website (used for error context).

    Returns:
        tuple: (bool, str) - A tuple containing:
            - bool: True if upload steps were successful, False otherwise
            - str: A message describing the result or error
    """
    try:
        print("-" * 40)
        print(f"Starting upload task for: {os.path.basename(file_path)} / {conference_name} on {website_url}") # Added website URL
        print(f"Current URL at task start: {driver.current_url}") # Added Log

        # Step 1: Navigate to BlockList page (or ensure we are on it)
        # It might be more efficient to navigate only if not already there,
        # but navigating each time is safer if previous uploads leave modals etc.
        print("Step 1: Navigating to BlockList page...")
        try:
            # Try clicking the link first
            blocklist_link = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "block"))
            )
            blocklist_link.click()
            print("Clicked BlockList link by ID.")
            print(f"Current URL after clicking block link: {driver.current_url}") # Added Log
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "demo-bootbox-prompt"))) # Wait for element on target page
            print("Target element 'demo-bootbox-prompt' found after navigation.") # Added Log
        except Exception as e:
             print(f"Navigating by ID 'block' failed: {e}. Current URL: {driver.current_url}. Trying alternative/refresh...") # Added URL
             try:
                 # Construct the expected URL (adjust if needed)
                 expected_url_part = "block_list" # Or the full URL if known
                 if expected_url_part not in driver.current_url:
                      alt_link = WebDriverWait(driver, 5).until(
                           EC.element_to_be_clickable((By.XPATH, "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'blocklist') or contains(@href, 'block')]"))
                      )
                      alt_link.click()
                      print("Clicked BlockList link using alternative selector.")
                      print(f"Current URL after clicking alternative link: {driver.current_url}") # Added Log
                      WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "demo-bootbox-prompt")))
                      print("Target element 'demo-bootbox-prompt' found after alternative navigation.") # Added Log
                 else:
                      print(f"Already on a blocklist-related page ({driver.current_url}). Proceeding.") # Added URL

             except Exception as e2:
                  print(f"All attempts to navigate to BlockList page failed: {e2}. Current URL: {driver.current_url}") # Added URL
                  # Try a hard refresh and navigate again as a last resort
                  try:
                      print("Attempting hard refresh and re-navigation...")
                      driver.refresh()
                      time.sleep(2)
                      blocklist_link = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, "block")))
                      blocklist_link.click()
                      print(f"Current URL after clicking block link post-refresh: {driver.current_url}") # Added Log
                      WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "demo-bootbox-prompt")))
                      print("Navigation successful after refresh. Target element found.") # Added Log
                  except Exception as e3:
                      print(f"Navigation failed even after refresh: {e3}. Current URL: {driver.current_url}") # Added URL
                      print("Suggestion: Check if the 'block' link ID and the 'demo-bootbox-prompt' element ID exist on this page.")
                      return False, "Navigation failed even after refresh" # Cannot proceed

        time.sleep(1) # Short pause after navigation

        # Step 2: Click the "Upload BlockList" button
        print("Step 2: Clicking 'Upload BlockList' button...")
        try:
            # Wait specifically for the button to be clickable
            upload_button = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.ID, "demo-bootbox-prompt"))
            )
            # Use JS click as a fallback for potential interception issues
            try:
                 upload_button.click()
            except ElementClickInterceptedException:
                 print("Standard click intercepted, trying JS click...")
                 driver.execute_script("arguments[0].click();", upload_button)
            print("Clicked 'Upload BlockList' button.")
        except Exception as e:
            print(f"Error finding/clicking 'Upload BlockList' button: {e}")
            traceback.print_exc()
            return False, "Error finding/clicking 'Upload BlockList' button" # Cannot proceed if button not found/clicked

        # Wait for modal dialog to appear and become stable
        print("Waiting for modal dialog...")
        try:
            modal = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.ID, "myModal"))
            )
            # Wait for an element within the modal to ensure it's loaded
            WebDriverWait(modal, 10).until(
                 EC.element_to_be_clickable((By.ID, "file"))
            )
            print("Modal dialog appeared.")
            time.sleep(0.5) # Extra short pause
        except TimeoutException:
            print("Error: Modal dialog did not appear or become ready.")
            return False, "Modal dialog did not appear"

        # Step 2.5: Select item from dropdown using conference_name
        print(f"Step 2.5: Selecting conference '{conference_name}' from dropdown...")
        try:
            # Find trigger within the modal
            dropdown_trigger = WebDriverWait(modal, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "span.select2-selection--single[aria-labelledby='select2-work-container']"))
            )
            dropdown_trigger.click()
            print("Clicked dropdown trigger.")
            time.sleep(1) # Wait for dropdown options to appear

            # Find option by exact text match
            option_text = conference_name
            dropdown_option_xpath = f"//li[contains(@class, 'select2-results__option')][normalize-space()='{option_text}']"
            # Wait for the option to be present in the results list (which might be outside the modal in the DOM)
            dropdown_option = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, dropdown_option_xpath))
            )

            # Scroll into view if necessary
            driver.execute_script("arguments[0].scrollIntoViewIfNeeded(true);", dropdown_option)
            time.sleep(0.5)

            dropdown_option.click()
            print(f"Selected option: {option_text}")
            # Wait for selection to reflect in the trigger element
            WebDriverWait(driver, 5).until(
                 EC.text_to_be_present_in_element_attribute((By.ID, "select2-work-container"), "title", option_text)
            )
            print("Dropdown selection confirmed.")
            time.sleep(0.5)

        except Exception as e:
            print(f"Error selecting dropdown option '{conference_name}': {e}")
            traceback.print_exc()
            # Try to close the modal before returning False if possible
            try:
                 close_button = modal.find_element(By.XPATH, ".//button[@data-dismiss='modal']")
                 close_button.click()
                 time.sleep(1)
            except:
                 pass # Ignore errors closing modal
            return False, f"Error selecting dropdown option '{conference_name}'"

        # Step 3: Find file upload element and send keys
        print("Step 3: Uploading file...")
        try:
            # Find element within the modal
            upload_element = modal.find_element(By.ID, "file")
            abs_file_path = os.path.abspath(file_path)
            print(f"Attempting to upload file: {abs_file_path}")

            # Ensure element is interactable
            driver.execute_script("arguments[0].style.display = 'block'; arguments[0].style.visibility = 'visible'; arguments[0].style.height = 'auto'; arguments[0].style.width = 'auto'; arguments[0].style.opacity = 1;", upload_element)
            driver.execute_script("arguments[0].value = '';", upload_element) # Clear field

            upload_element.send_keys(abs_file_path)
            print("File path sent using send_keys.")
            time.sleep(1) # Short wait

            # Verification
            selected_file_value = upload_element.get_attribute('value')
            if not selected_file_value or os.path.basename(abs_file_path) not in selected_file_value:
                 print(f"Warning: File selection verification failed after send_keys. Input value: '{selected_file_value}'")
                 # If send_keys fails silently, this task fails here. PyAutoGUI isn't feasible in this refactored structure easily.
                 raise Exception("File path not set correctly in input field.")
            else:
                 print(f"File selection confirmed in input field: {selected_file_value}")

        except Exception as e:
            print(f"File upload step failed: {e}")
            traceback.print_exc()
            # Try to close modal
            try:
                 close_button = modal.find_element(By.XPATH, ".//button[@data-dismiss='modal']")
                 close_button.click()
                 time.sleep(1)
            except:
                 pass
            return False, "File upload step failed"

        # Step 4: Submit the form
        print("Step 4: Clicking 'Upload File' button...")
        try:
            # Find submit button within the modal
            submit_button = WebDriverWait(modal, 10).until(
                EC.element_to_be_clickable((By.XPATH, ".//button[@onclick='uploadfile()']"))
            )
            submit_button.click()
            print("Clicked 'Upload File' button.")
        except Exception as e:
            print(f"Error clicking 'Upload File' button: {e}")
            traceback.print_exc()
            # Try to close modal if submit fails
            try:
                 close_button = modal.find_element(By.XPATH, ".//button[@data-dismiss='modal']")
                 close_button.click()
                 time.sleep(1)
            except:
                 pass
            return False, "Error clicking 'Upload File' button"

        # Wait for processing and check for success (modal should close)
        print("Waiting for upload processing (modal to close)...")
        modal_closed = False
        try:
            WebDriverWait(driver, 15).until(
                EC.invisibility_of_element_located((By.ID, "myModal"))
            )
            print("Modal dialog closed.")
            modal_closed = True
        except TimeoutException:
            print("Warning: Modal dialog did not close after submitting.")
            pass

        time.sleep(1)  # Extra wait after modal closes or timeout

        # Check for success message
        try:
            success_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'alert-success')]"))
            )
            print(f"Upload successful! Indicator found: {success_element.text[:100]}")
            print("Waiting 1 second after upload...")
            time.sleep(1)
            return True, "Upload completed successfully"
        except TimeoutException:
            # Check for error messages
            try:
                error_element = driver.find_element(By.XPATH, "//div[contains(@class, 'alert-danger')]")
                error_message = error_element.text[:100]
                print(f"Found error message on page: {error_message}")
                return False, f"Upload failed: {error_message}"
            except NoSuchElementException:
                if modal_closed:
                    print("Modal closed but no explicit success/error message found. Assuming success.")
                    return True, "Upload likely successful (modal closed)"
                else:
                    print("No specific success or error message found on page.")
                    return False, "Upload status unclear"

    except Exception as e:
        error_msg = f"Upload error: {str(e)}"
        print(f"An unexpected error occurred: {error_msg}")
        traceback.print_exc()
        print("Waiting 1 second after error...")
        time.sleep(1)
        return False, error_msg

def navigate_to_blocklist(driver, site_url):
    """Ensure proper navigation to blocklist section after login."""
    try:
        # First ensure we're on the correct site
        current_url = driver.current_url
        if site_url not in current_url:
            driver.get(site_url)
            time.sleep(2)  # Allow for page load

        # Click the blocklist link/button
        blocklist_element = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.ID, "block"))
        )
        blocklist_element.click()

        # Wait for the blocklist page to load
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.ID, "myModal"))  # Adjust selector as needed
        )
        return True
    except Exception as e:
        print(f"Failed to navigate to blocklist: {e}")
        return False

def perform_site_login(driver, site):
    """Login implementation based on working upl_unsubs_pp.py version."""
    try:
        print(f"Attempting login for {site['name']}...")

        # Navigate to the website
        print(f"Navigating to website: {site['url']}")
        driver.get(site['url'])

        # Login
        print(f"Logging in with username: {site['username']}")
        driver.find_element(By.ID, "name").send_keys(site['username'])
        driver.find_element(By.ID, "password").send_keys(site['password'])
        driver.find_element(By.XPATH, "//button[@id='login']").click()
        time.sleep(2)

        # Verify login success
        if "login" not in driver.current_url.lower():
            print("Login successful")
            return True
        else:
            print("Login failed - still on login page")
            return False

    except Exception as e:
        print(f"Login failed for {site['name']}: {e}")
        traceback.print_exc()
        return False

def setup_browser(headless=True):  # Changed default to False
    """Configure and return ChromeDriver with proper options.

    Args:
        headless (bool): Whether to run browser in headless mode

    Returns:
        webdriver.Chrome: Configured Chrome WebDriver instance
    """
    options = webdriver.ChromeOptions()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])

    if headless:
        options.add_argument("--headless=new")  # Updated to new headless mode
        options.add_argument("--window-size=1920,1080")
        print("Running in headless mode")
    else:
        print("Running in visible mode")

    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")

    driver = webdriver.Chrome(options=options)
    driver.set_window_size(1920, 1080)  # Ensure proper rendering
    return driver

def process_website(site, config_df, progress, main_task_id, headless_mode=True):
    """Process a single website with proper cleanup, updating progress."""
    site_name = site['name']
    site_url = site['url']
    driver = None
    result_tracker = ResultTracker()  # Use the ResultTracker class

    try:
        # Initialize new browser instance for each site
        driver = setup_browser(headless_mode)

        # Perform login with retry
        login_attempts = 0
        max_attempts = 3
        login_successful = False

        while login_attempts < max_attempts:
            print(f"\nLogin attempt {login_attempts + 1} of {max_attempts}")
            if perform_site_login(driver, site):
                login_successful = True
                break
            login_attempts += 1
            if login_attempts < max_attempts:
                print(f"Login failed. Waiting 10 seconds before retry...")
                time.sleep(10)
                driver.delete_all_cookies()
                driver.refresh()
                time.sleep(3)

        if not login_successful:
            # Add login failure result for all entries
            for index, row in config_df.iterrows():
                conference = str(row["Conference Name"]).strip()
                entry_key = f"Entry_{index+1}_{conference[:20]}"
                result_tracker.add_result(site_name, UploadResult(
                    entry_key=entry_key,
                    status=UploadStatus.FAILED_LOGIN,
                    message="Failed to login after multiple attempts"
                ))
            return result_tracker.results[site_name]

        # Process each file
        for index, row in config_df.iterrows():
            conference = str(row["Conference Name"]).strip()
            file_to_upload = str(row["File Name"]).strip()
            entry_key = f"Entry_{index+1}_{conference[:20]}"

            # Check if file exists
            if not os.path.exists(file_to_upload):
                result_tracker.add_result(site_name, UploadResult(
                    entry_key=entry_key,
                    status=UploadStatus.SKIPPED_NOT_FOUND,
                    message=f"File not found: {file_to_upload}"
                ))
                continue

            # Verify navigation before each upload
            if not navigate_to_blocklist(driver, site_url):
                result_tracker.add_result(site_name, UploadResult(
                    entry_key=entry_key,
                    status=UploadStatus.FAILED,
                    message="Navigation failed"
                ))
                continue

            success, message = perform_single_upload_task(
                driver=driver,
                file_path=file_to_upload,
                conference_name=conference,
                website_url=site_url
            )

            result_tracker.add_result(site_name, UploadResult(
                entry_key=entry_key,
                status=UploadStatus.SUCCESS if success else UploadStatus.FAILED,
                message=message
            ))

            # Update progress bar after processing this entry
            progress.update(main_task_id, advance=1)

            # Wait between uploads
            time.sleep(3)

        return result_tracker.results[site_name]

    except Exception as e:
        print(f"Error processing {site_name}: {e}")
        traceback.print_exc()
        # Add error result
        result_tracker.add_result(site_name, UploadResult(
            entry_key="SITE_ERROR",
            status=UploadStatus.FAILED,
            message=str(e)
        ))
        return result_tracker.results[site_name]
    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass

if __name__ == "__main__":
    # Path to the CSV file
    csv_file_path = "blocklist.csv" # Assumes CSV is in the same directory as the script
    headless_mode = True  # Changed from True to False to run in visible mode

    # Define websites here
    websites = [
        {"name": "Magnus Group Port1", "url": "https://magnusgroup.port1.in", "username": "admin", "password": "Admin@890"}
    ]

    # Shuffle the websites list
    random.shuffle(websites)

    print("\nProcessing websites in the following order:")
    for i, site in enumerate(websites, 1):
        print(f"{i}. {site['name']} ({site['url']})")
    print("\n")

    # Check if CSV file exists
    if not os.path.exists(csv_file_path):
        print(f"Error: CSV file not found at '{os.path.abspath(csv_file_path)}'")
        exit()

    print(f"Reading configuration from: {csv_file_path}")
    try:
        config_df = pd.read_csv(csv_file_path)
        if "Conference Name" not in config_df.columns or "File Name" not in config_df.columns:
             print("Error: CSV must contain 'Conference Name' and 'File Name' columns.")
             exit()
        config_df.fillna('', inplace=True)
        print(f"Found {len(config_df)} entries to process.")
    except Exception as e:
        print(f"Error reading or processing CSV file: {e}")
        exit()

    # Store detailed results
    overall_results = {} # {website_name: [UploadResult]}

    # --- Progress Bar Setup ---
    total_tasks = len(websites) * len(config_df)
    progress_columns = [
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TextColumn("({task.completed} of {task.total})"),
        TimeRemainingColumn(),
        TimeElapsedColumn(),
    ]
    progress = Progress(*progress_columns, transient=False) # Keep progress visible after completion

    # --- New Iteration Logic with Progress ---
    with progress:
        main_task_id = progress.add_task("[cyan]Overall Upload Progress", total=total_tasks)

        # Loop through each website
        for site in websites:
            print("\n" + "#" * 70)
            print(f"# Processing Website: {site['name']} ({site['url']})")
            print("#" * 70)

            # Process website with new isolated browser instance, passing progress info
            site_results = process_website(site, config_df, progress, main_task_id, headless_mode)
            overall_results[site['name']] = site_results

            # Longer wait between websites
        if site != websites[-1]:
            print(f"\nWaiting 15 seconds before starting next website...")
            time.sleep(15)


    # --- Final Summary ---
    print("\n" + "=" * 70)
    print("= OVERALL UPLOAD PROCESS SUMMARY (Grouped by Website)")
    print("=" * 70)

    final_all_successful = True
    for site_name, site_results in overall_results.items():
        print(f"\n=== Website: {site_name} ===")
        site_success_count = sum(1 for result in site_results if result.status == UploadStatus.SUCCESS)
        site_fail_count = sum(1 for result in site_results if result.status in
                            [UploadStatus.FAILED, UploadStatus.FAILED_LOGIN])
        site_skip_count = sum(1 for result in site_results if result.status in
                            [UploadStatus.SKIPPED_MISSING, UploadStatus.SKIPPED_NOT_FOUND])

        for result in site_results:
            status_str = f"{result.status.value}"
            if result.message:
                status_str += f" - {result.message}"
            print(f"{result.entry_key}: {status_str}")

        print(f"--- Summary for {site_name}: {site_success_count} SUCCESS, {site_fail_count} FAILED, {site_skip_count} SKIPPED ---")

        # Only mark as failed if there are actual failures (not counting skips)
        if site_fail_count > 0:
            final_all_successful = False

    print("\n" + "=" * 70)
    if final_all_successful:
        print("All uploads across all websites completed successfully!")
    else:
        print("Some uploads failed. Check the detailed logs above.")
    print("=" * 70)



